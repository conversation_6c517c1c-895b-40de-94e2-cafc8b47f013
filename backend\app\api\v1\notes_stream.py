"""
流式内容提取API
支持Server-Sent Events (SSE)，分阶段返回处理结果
"""
import asyncio
import json
import logging
import time
from typing import Dict, Any, Optional, AsyncGenerator
from urllib.parse import urlparse

from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.responses import StreamingResponse, Response
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from app.core.database import get_db
from app.models.user import User
from app.api.deps import get_current_user
from app.services.stream_task_manager import stream_task_manager, ProcessingStage
from app.utils.platform_detector import detect_platform, extract_platform_id
from app.services.streaming_ai_analyzer import streaming_ai_analyzer
from app.services.xiaohongshu_extractor import xiaohongshu_extractor
from app.utils.audio_processor import audio_processor
from app.utils.platform_detector import detect_platform
from app.core.permissions import check_user_permissions, quota_manager, PermissionError

logger = logging.getLogger(__name__)

router = APIRouter()


class StreamNoteRequest(BaseModel):
    """流式笔记提取请求"""
    url: str = Field(..., description="笔记URL")
    custom_analysis_prompt: Optional[str] = Field(None, description="自定义AI分析提示词")
    force_refresh: bool = Field(False, description="是否强制刷新缓存")
    stream_mode: bool = Field(True, description="是否使用流式模式")
    cookie: Optional[str] = Field(None, description="小红书Cookie（可选）")


class TaskStatusRequest(BaseModel):
    """任务状态查询请求"""
    task_id: str = Field(..., description="任务ID")


@router.options("/stream")
async def stream_options():
    """处理流式API的OPTIONS预检请求"""
    return Response(
        status_code=200,
        headers={
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept",
            "Access-Control-Max-Age": "86400"
        }
    )


@router.post("/stream")
async def stream_extract_note(
    request: StreamNoteRequest,
    req: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    流式内容提取接口

    支持Server-Sent Events，分阶段返回处理结果：
    1. 平台识别
    2. URL解析和基础信息提取
    3. 视频转录（如果有视频）
    4. AI分析（流式输出）
    """

    # 只支持流式模式
    if not request.stream_mode:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="此接口仅支持流式模式，请设置 stream_mode=true"
        )

    # 创建流式任务
    try:
        task_id = await stream_task_manager.create_task(
            user_id=current_user.id,
            original_url=request.url,
            custom_analysis_prompt=request.custom_analysis_prompt,
            force_refresh=request.force_refresh,
            client_info={
                "user_agent": req.headers.get("user-agent"),
                "ip": req.client.host if req.client else None
            }
        )

        logger.info(f"🚀 Created streaming task: {task_id}")

        # 返回SSE流
        return StreamingResponse(
            _stream_processing_events(task_id, request, current_user, db),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )

    except Exception as e:
        logger.error(f"❌ Failed to create streaming task: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create streaming task: {str(e)}"
        )


async def _stream_processing_events(
    task_id: str,
    request: StreamNoteRequest,
    current_user: User,
    db: Session
) -> AsyncGenerator[str, None]:
    """生成流式处理事件"""

    try:
        # 发送初始事件
        yield _format_sse_event("task_created", {
            "task_id": task_id,
            "message": "任务已创建，开始处理...",
            "timestamp": time.time()
        })

        # 阶段1: 平台识别
        yield _format_sse_event("stage_start", {
            "stage": "platform_detection",
            "message": "正在识别平台类型...",
            "progress": 5
        })

        platform = detect_platform(request.url)
        if not platform:
            raise Exception("不支持的平台URL")

        await stream_task_manager.update_task_progress(
            task_id, ProcessingStage.PLATFORM_DETECTION, 10,
            {"platform": platform}
        )

        yield _format_sse_event("stage_complete", {
            "stage": "platform_detection",
            "result": {"platform": platform},
            "message": f"平台识别完成: {platform}",
            "progress": 10
        })

        # 阶段2: URL解析和内容提取
        yield _format_sse_event("stage_start", {
            "stage": "content_extraction",
            "message": "正在提取基础内容信息...",
            "progress": 15
        })

        # 初始化变量
        note_data = None
        from_cache = False

        # 提取内容
        if platform == "xiaohongshu":
            # 使用与同步API相同的小红书提取逻辑，包括cookie支持
            from app.services.xiaohongshu_extractor import xiaohongshu_extractor
            note_data = await xiaohongshu_extractor.extract_content_from_url(
                url=request.url,
                cookie=request.cookie  # 使用请求中的cookie
            )
        elif platform == "douyin":
            # 使用抖音提取器
            from app.services.douyin_extractor import douyin_extractor
            note_data = await douyin_extractor.extract_content_from_url(request.url)

        if not note_data:
            raise Exception("内容提取失败")

        # 检查用户缓存（在获取到真实note_id后）
        if not request.force_refresh:
            from app.services.content_storage_service import content_storage_service
            note_id = note_data.get('note_id') or note_data.get('video_id') or note_data.get('aweme_id')
            if note_id:
                cached_data = await content_storage_service.get_cached_note(
                    platform=platform,
                    note_id=note_id,
                    user_id=current_user.id,
                    original_url=request.url
                )
                if cached_data:
                    logger.info(f"🎯 命中用户缓存: {platform} - {note_id} - 用户{current_user.id}")

                    # 直接返回缓存的完整结果
                    await stream_task_manager.update_task_progress(
                        task_id, ProcessingStage.AI_ANALYSIS, 100,
                        {
                            "note_data": cached_data['note_data'],
                            "transcript_text": cached_data['transcript_text'],
                            "ai_analysis": cached_data['ai_analysis'],
                            "from_cache": True,
                            "cached_at": cached_data.get('cached_at')
                        }
                    )

                    yield _format_sse_event("stage_complete", {
                        "stage": "cache_hit",
                        "result": {
                            "note_data": cached_data['note_data'],
                            "transcript_text": cached_data['transcript_text'],
                            "ai_analysis": cached_data['ai_analysis'],
                            "from_cache": True,
                            "cached_at": cached_data.get('cached_at')
                        },
                        "message": "使用用户缓存数据",
                        "progress": 100
                    })

                    yield _format_sse_event("complete", {
                        "message": "所有处理完成（来自缓存）",
                        "final_result": {
                            "task_id": task_id,
                            "platform": platform,
                            "url": request.url,
                            "note_data": cached_data['note_data'],
                            "transcript_text": cached_data['transcript_text'],
                            "ai_analysis": cached_data['ai_analysis'],
                            "from_cache": True
                        },
                        "processing_metadata": {
                            "from_cache": True,
                            "processing_time": time.time(),
                            "user_id": current_user.id
                        }
                    })
                    # 缓存命中，直接结束处理
                    return

        await stream_task_manager.update_task_progress(
            task_id, ProcessingStage.CONTENT_EXTRACTION, 30,
            {"note_data": note_data, "from_cache": from_cache}
        )

        yield _format_sse_event("stage_complete", {
            "stage": "content_extraction",
            "result": {
                "title": note_data.get("title"),
                "description": note_data.get("description"),
                "author": note_data.get("author"),
                "media_info": {
                    "has_video": bool(note_data.get("video_url")),
                    "image_count": len(note_data.get("images", []))
                },
                "from_cache": from_cache
            },
            "message": "基础信息提取完成",
            "progress": 30
        })

        # 阶段3: 视频转录（如果有视频）
        video_url = await _detect_video_url(note_data)
        transcript_text = None

        logger.info(f"🎬 Video detection result: {video_url}")
        logger.info(f"🎬 Note data type: {note_data.get('type')}")
        logger.info(f"🎬 Note data status: {note_data.get('status')}")

        # 权限检查：转录时长配额
        estimated_minutes = 0
        if video_url:
            # 估算视频时长（默认5分钟，实际应该从视频信息中获取）
            estimated_minutes = 5  # 可以根据视频时长API获取更准确的值

            # 检查转录配额
            try:
                permission_check = await check_user_permissions(
                    user_id=current_user.id,
                    required_minutes=estimated_minutes,
                    required_credits=0  # 转录阶段不消耗积分
                )

                if not permission_check["overall_check"]:
                    error_msg = "; ".join(permission_check["errors"])
                    raise PermissionError(error_msg, permission_check)

            except PermissionError as e:
                logger.warning(f"⚠️ 用户{current_user.id}转录配额不足: {e.detail}")

                yield _format_sse_event("quota_exceeded", {
                    "stage": "video_transcription",
                    "error": e.detail,
                    "message": "转录配额不足，请升级账户",
                    "progress": 35
                })

                await stream_task_manager.update_task_progress(
                    task_id, ProcessingStage.VIDEO_TRANSCRIPTION, 35,
                    {"error": "quota_exceeded", "details": e.detail}
                )

                raise e

        if video_url:
            yield _format_sse_event("stage_start", {
                "stage": "video_transcription",
                "message": "检测到视频，正在进行转录...",
                "progress": 35
            })

            try:
                # 使用简化的转录函数
                transcript_text = await _transcribe_video_simple(
                    video_url,
                    force_refresh=request.force_refresh
                )

                await stream_task_manager.update_task_progress(
                    task_id, ProcessingStage.VIDEO_TRANSCRIPTION, 50,
                    {"transcript_text": transcript_text}
                )

                if transcript_text:
                    # 转录成功，扣除转录时长配额
                    note_id = note_data.get('note_id') or note_data.get('video_id') or note_data.get('aweme_id')
                    transcription_success = await quota_manager.consume_transcription_quota(
                        user_id=current_user.id,
                        minutes_consumed=estimated_minutes,
                        task_id=task_id,
                        note_id=note_id,
                        platform=platform,
                        transcription_duration=estimated_minutes * 60  # 转换为秒
                    )

                    if not transcription_success:
                        logger.error(f"❌ 转录配额扣除失败: 用户{current_user.id}")
                    else:
                        logger.info(f"✅ 转录配额扣除成功: 用户{current_user.id}消耗{estimated_minutes}分钟")

                    yield _format_sse_event("stage_complete", {
                        "stage": "video_transcription",
                        "result": {
                            "transcript_text": transcript_text,
                            "transcript_length": len(transcript_text),
                            "minutes_consumed": estimated_minutes
                        },
                        "message": f"视频转录完成，获得 {len(transcript_text)} 字符，消耗 {estimated_minutes} 分钟配额",
                        "progress": 50
                    })
                else:
                    yield _format_sse_event("stage_error", {
                        "stage": "video_transcription",
                        "message": "视频转录失败，将继续其他分析",
                        "progress": 50
                    })

            except Exception as e:
                logger.error(f"❌ Video transcription failed: {e}")
                yield _format_sse_event("stage_error", {
                    "stage": "video_transcription",
                    "error": str(e),
                    "message": "视频转录失败，将继续其他分析",
                    "progress": 50
                })
        else:
            # 检查是否是因为内容提取失败导致的
            if note_data.get('status') == 'partial_extraction':
                yield _format_sse_event("stage_skip", {
                    "stage": "video_transcription",
                    "message": "内容提取不完整，无法获取视频信息，跳过转录",
                    "progress": 50
                })
            else:
                yield _format_sse_event("stage_skip", {
                    "stage": "video_transcription",
                    "message": "无视频内容，跳过转录",
                    "progress": 50
                })

        # 阶段4: AI分析（流式输出）
        yield _format_sse_event("stage_start", {
            "stage": "ai_analysis",
            "message": "开始AI智能分析...",
            "progress": 55
        })

        # 权限检查：积分配额
        # 估算AI分析所需积分（根据内容长度估算）
        content_length = len(note_data.get('description', '')) + len(transcript_text or '')
        estimated_input_tokens = content_length // 4  # 粗略估算：4字符=1token
        estimated_output_tokens = 2000  # 估算输出token数

        # 计算预估积分消耗
        estimated_credits = await quota_manager.calculate_credits_cost(
            model_name="deepseek-v3",  # 使用DeepSeek V3模型计算
            input_tokens=estimated_input_tokens,
            output_tokens=estimated_output_tokens,
            model_provider="deepseek"
        )

        # 检查积分配额
        try:
            permission_check = await check_user_permissions(
                user_id=current_user.id,
                required_minutes=0,  # AI分析阶段不需要转录时长
                required_credits=estimated_credits
            )

            if not permission_check["overall_check"]:
                error_msg = "; ".join(permission_check["errors"])
                raise PermissionError(error_msg, permission_check)

        except PermissionError as e:
            logger.warning(f"⚠️ 用户{current_user.id}积分配额不足: {e.detail}")

            yield _format_sse_event("quota_exceeded", {
                "stage": "ai_analysis",
                "error": e.detail,
                "message": "积分配额不足，请升级账户",
                "progress": 55
            })

            await stream_task_manager.update_task_progress(
                task_id, ProcessingStage.AI_ANALYSIS, 55,
                {"error": "quota_exceeded", "details": e.detail}
            )

            raise e

        ai_analysis_result = {}
        base_progress = 55
        analysis_progress_range = 40  # AI分析占40%的进度

        try:
            # 流式AI分析
            async for chunk in streaming_ai_analyzer.analyze_content_streaming(
                note_data=note_data,
                transcript_text=transcript_text,
                custom_prompt=request.custom_analysis_prompt,
                platform=platform
            ):
                # 计算当前进度
                current_progress = base_progress + int(
                    (chunk.progress_percentage / 100) * analysis_progress_range
                )

                # 保存分析结果
                if chunk.is_complete and chunk.chunk_type != "error":
                    ai_analysis_result[chunk.chunk_type] = chunk.chunk_data["result"]

                # 区分流式内容和完成事件
                if chunk.chunk_type.endswith("_streaming"):
                    # 流式内容块
                    yield _format_sse_event("ai_analysis_streaming", {
                        "chunk_type": chunk.chunk_type,
                        "module_name": chunk.chunk_data.get('module_name'),
                        "content_chunk": chunk.chunk_data.get('content_chunk'),
                        "accumulated_content": chunk.chunk_data.get('accumulated_content'),
                        "progress": current_progress,
                        "message": f"正在分析: {chunk.chunk_data.get('module_name', chunk.chunk_type)}"
                    })
                else:
                    # 模块完成事件
                    yield _format_sse_event("ai_analysis_chunk", {
                        "chunk_type": chunk.chunk_type,
                        "chunk_data": chunk.chunk_data,
                        "is_complete": chunk.is_complete,
                        "progress": current_progress,
                        "message": f"正在分析: {chunk.chunk_data.get('module_name', chunk.chunk_type)}"
                    })

                # 更新任务进度
                await stream_task_manager.update_task_progress(
                    task_id, ProcessingStage.AI_ANALYSIS, current_progress,
                    {"current_module": chunk.chunk_type, "partial_result": ai_analysis_result}
                )

            # AI分析完成，扣除积分配额
            note_id = note_data.get('note_id') or note_data.get('video_id') or note_data.get('aweme_id')

            # 实际消耗的积分（这里使用估算值，实际应该从AI服务获取真实token消耗）
            actual_credits_consumed = estimated_credits

            credits_success = await quota_manager.consume_credits_quota(
                user_id=current_user.id,
                credits_consumed=actual_credits_consumed,
                task_id=task_id,
                note_id=note_id,
                platform=platform,
                model_name="deepseek-v3",  # 实际使用的DeepSeek V3模型
                input_tokens=estimated_input_tokens,
                output_tokens=estimated_output_tokens
            )

            if not credits_success:
                logger.error(f"❌ 积分配额扣除失败: 用户{current_user.id}")
            else:
                logger.info(f"✅ 积分配额扣除成功: 用户{current_user.id}消耗{actual_credits_consumed}积分")

            await stream_task_manager.update_task_progress(
                task_id, ProcessingStage.AI_ANALYSIS, 95,
                {"ai_analysis": ai_analysis_result}
            )

            yield _format_sse_event("stage_complete", {
                "stage": "ai_analysis",
                "result": ai_analysis_result,
                "message": f"AI分析完成，消耗 {actual_credits_consumed} 积分",
                "progress": 95,
                "credits_consumed": actual_credits_consumed
            })

        except Exception as e:
            logger.error(f"❌ AI analysis failed: {e}")
            yield _format_sse_event("stage_error", {
                "stage": "ai_analysis",
                "error": str(e),
                "message": "AI分析失败，返回基础结果",
                "progress": 95
            })

        # 保存数据到数据库
        try:
            from app.services.content_storage_service import content_storage_service

            logger.info(f"🔄 开始保存数据到数据库: {platform}")

            if platform == "xiaohongshu":
                saved_note, is_new = await content_storage_service.save_xiaohongshu_note(
                    note_data=note_data,
                    transcript_text=transcript_text,
                    ai_analysis=ai_analysis_result,
                    analysis_prompt=request.custom_analysis_prompt,
                    user_id=current_user.id
                )
                logger.info(f"💾 小红书数据保存完成: {'新建' if is_new else '更新'} - ID: {saved_note.note_id}")

            elif platform == "douyin":
                saved_note, is_new = await content_storage_service.save_douyin_note(
                    note_data=note_data,
                    transcript_text=transcript_text,
                    ai_analysis=ai_analysis_result,
                    analysis_prompt=request.custom_analysis_prompt,
                    user_id=current_user.id
                )
                logger.info(f"💾 抖音数据保存完成: {'新建' if is_new else '更新'} - ID: {saved_note.note_id}")

        except Exception as e:
            logger.error(f"❌ 数据保存失败: {e}")
            import traceback
            logger.error(f"❌ 详细错误信息: {traceback.format_exc()}")
            # 不影响主流程，继续执行

        # 最终结果整合
        final_result = {
            "task_id": task_id,
            "platform": platform,
            "url": request.url,
            "note_data": note_data,
            "transcript_text": transcript_text,
            "ai_analysis": ai_analysis_result,
            "processing_metadata": {
                "from_cache": from_cache,
                "processing_time": time.time(),
                "user_id": current_user.id
            }
        }

        # 保存最终结果
        await stream_task_manager.set_task_result(task_id, final_result)

        # 发送完成事件
        yield _format_sse_event("task_complete", {
            "task_id": task_id,
            "final_result": final_result,
            "message": "所有处理完成",
            "progress": 100
        })

    except Exception as e:
        logger.error(f"❌ Streaming processing failed: {e}")

        # 更新任务状态为失败
        await stream_task_manager.update_task_progress(
            task_id, ProcessingStage.AI_ANALYSIS, 0,
            error_message=str(e)
        )

        # 发送错误事件
        yield _format_sse_event("task_error", {
            "task_id": task_id,
            "error": str(e),
            "message": "处理过程中发生错误",
            "progress": 0
        })


def _format_sse_event(event_type: str, data: Dict[str, Any]) -> str:
    """格式化SSE事件"""
    return f"event: {event_type}\ndata: {json.dumps(data, ensure_ascii=False)}\n\n"


@router.get("/task/{task_id}")
async def get_task_status(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """获取任务状态"""
    try:
        task_data = await stream_task_manager.get_task(task_id)

        if not task_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        # 检查权限
        if task_data["user_id"] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        return {
            "success": True,
            "data": task_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get task status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get("/tasks")
async def get_user_tasks(
    limit: int = 10,
    current_user: User = Depends(get_current_user)
):
    """获取用户的任务列表"""
    try:
        tasks = await stream_task_manager.get_user_tasks(current_user.id, limit)

        return {
            "success": True,
            "data": tasks,
            "count": len(tasks)
        }

    except Exception as e:
        logger.error(f"❌ Failed to get user tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post("/reconnect/{task_id}")
async def reconnect_task_stream(
    task_id: str,
    current_user: User = Depends(get_current_user)
):
    """重连任务流（用于客户端重连）"""
    try:
        task_data = await stream_task_manager.get_task(task_id)

        if not task_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Task not found"
            )

        # 检查权限
        if task_data["user_id"] != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )

        # 返回任务当前状态的SSE流
        return StreamingResponse(
            _reconnect_stream_events(task_data),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to reconnect task stream: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


async def _reconnect_stream_events(task_data: Dict[str, Any]) -> AsyncGenerator[str, None]:
    """生成重连流事件"""

    # 发送当前任务状态
    yield _format_sse_event("reconnect", {
        "task_id": task_data["task_id"],
        "status": task_data["status"],
        "current_stage": task_data["current_stage"],
        "progress": task_data["progress_percentage"],
        "message": "已重连到任务流"
    })

    # 如果任务已完成，发送最终结果
    if task_data["status"] == "completed" and task_data.get("final_result"):
        yield _format_sse_event("task_complete", {
            "task_id": task_data["task_id"],
            "final_result": task_data["final_result"],
            "message": "任务已完成",
            "progress": 100
        })

    # 如果任务失败，发送错误信息
    elif task_data["status"] == "failed":
        yield _format_sse_event("task_error", {
            "task_id": task_data["task_id"],
            "error": task_data.get("error_message", "Unknown error"),
            "message": "任务执行失败",
            "progress": 0
        })

    # 如果任务仍在进行中，发送当前进度
    elif task_data["status"] == "processing":
        yield _format_sse_event("task_progress", {
            "task_id": task_data["task_id"],
            "current_stage": task_data["current_stage"],
            "progress": task_data["progress_percentage"],
            "stage_results": task_data.get("stage_results", {}),
            "message": f"任务进行中: {task_data['current_stage']}"
        })


async def _transcribe_video_simple(video_url: str, force_refresh: bool = False) -> Optional[str]:
    """
    简化的视频转录函数，使用FFmpeg直接从URL提取音频并转录

    Args:
        video_url: 视频URL
        force_refresh: 是否强制刷新

    Returns:
        转录文本或None
    """
    try:
        logger.info(f"🎤 开始转录视频: {video_url[:100]}...")

        # 1. 使用FFmpeg直接从URL提取音频
        logger.info("📥 使用FFmpeg从URL提取音频...")
        audio_path = await audio_processor.download_video_audio(video_url)

        if not audio_path:
            logger.error("❌ 音频提取失败")
            return None

        logger.info(f"✅ 音频提取成功: {audio_path}")

        # 2. 使用本地ASR转录音频
        logger.info("🎤 开始本地ASR转录...")
        transcript_text = await _transcribe_audio_with_local_asr(audio_path)

        # 3. 清理临时文件
        try:
            import os
            if os.path.exists(audio_path):
                os.remove(audio_path)
                logger.info("🗑️ 临时音频文件已清理")
        except Exception as e:
            logger.warning(f"⚠️ 清理临时文件失败: {e}")

        if transcript_text:
            logger.info(f"✅ 转录成功: {len(transcript_text)} 字符")
            return transcript_text.strip()
        else:
            logger.error("❌ 转录失败")
            return None

    except Exception as e:
        logger.error(f"❌ 视频转录失败: {e}")
        import traceback
        logger.error(f"❌ 详细错误: {traceback.format_exc()}")
        return None


async def _transcribe_audio_with_local_asr(audio_path: str) -> Optional[str]:
    """
    使用本地ASR转录音频文件

    Args:
        audio_path: 音频文件路径

    Returns:
        转录文本或None
    """
    try:
        # 在线程池中执行同步的转录操作
        import asyncio
        from concurrent.futures import ThreadPoolExecutor

        def transcribe_sync():
            try:
                # 导入本地ASR模块
                import sys
                import os
                totext_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'totext')
                if totext_path not in sys.path:
                    sys.path.insert(0, totext_path)

                from totext import transcribe_audio

                logger.info(f"🎤 调用totext.transcribe_audio: {audio_path}")

                # 执行转录
                result = transcribe_audio(
                    audio_path=audio_path,
                    language="auto",  # 自动检测语言
                    use_itn=True,     # 使用逆文本标准化
                    merge_vad=True    # 合并VAD结果
                )

                logger.info(f"🎤 转录结果类型: {type(result)}, 长度: {len(result) if result else 0}")
                return result

            except Exception as e:
                logger.error(f"❌ 同步转录错误: {e}")
                import traceback
                logger.error(f"❌ 转录错误详情: {traceback.format_exc()}")
                return None

        # 在线程池中执行转录
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            transcript_text = await loop.run_in_executor(executor, transcribe_sync)

        return transcript_text

    except Exception as e:
        logger.error(f"❌ 本地ASR转录失败: {e}")
        return None


async def _detect_video_url(note_data: dict) -> Optional[str]:
    """
    检测视频URL

    Args:
        note_data: 笔记数据

    Returns:
        视频URL或None
    """
    # 检测视频URL - 支持多种字段名称
    video_url = (
        note_data.get("video_url") or
        note_data.get("video") or
        note_data.get("videoUrl")
    )

    # 如果没有直接的视频URL，检查media_urls
    if not video_url and note_data.get("media_urls"):
        for url in note_data.get("media_urls", []):
            if "video" in str(url).lower() or url.endswith(('.mp4', '.mov', '.avi', '.mkv')):
                video_url = url
                break

    return video_url