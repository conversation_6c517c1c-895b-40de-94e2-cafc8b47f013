"""
流式AI分析服务
支持分块流式输出，确保JSON格式正确性
"""
import asyncio
import json
import logging
import re
from typing import Dict, Any, Optional, AsyncGenerator, List
from dataclasses import dataclass

# 简化版本，不依赖AI服务

logger = logging.getLogger(__name__)


@dataclass
class AnalysisChunk:
    """分析块数据结构"""
    chunk_type: str  # 块类型：content_theme, content_structure, creative_highlights, etc.
    chunk_data: Dict[str, Any]  # 块数据
    is_complete: bool  # 是否完整
    progress_percentage: int  # 进度百分比


class StreamingAIAnalyzer:
    """流式AI分析器"""

    def __init__(self):
        self.analysis_modules = [
            {
                "name": "content_theme",
                "display_name": "内容主题分析",
                "weight": 25,
                "prompt_section": "内容主题分析"
            },
            {
                "name": "content_structure",
                "display_name": "文案结构分析",
                "weight": 20,
                "prompt_section": "文案结构分析"
            },
            {
                "name": "creative_highlights",
                "display_name": "创意亮点",
                "weight": 20,
                "prompt_section": "创意亮点"
            },
            {
                "name": "engagement_strategy",
                "display_name": "传播策略",
                "weight": 15,
                "prompt_section": "传播策略"
            },
            {
                "name": "emotional_analysis",
                "display_name": "情感倾向",
                "weight": 10,
                "prompt_section": "情感倾向"
            },
            {
                "name": "optimization_suggestions",
                "display_name": "优化建议",
                "weight": 10,
                "prompt_section": "优化建议"
            }
        ]

    async def analyze_content_streaming(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str] = None,
        custom_prompt: Optional[str] = None,
        platform: str = "xiaohongshu"
    ) -> AsyncGenerator[AnalysisChunk, None]:
        """简化版流式分析，返回模拟结果"""

        try:
            logger.info(f"🤖 Starting simplified streaming analysis for {platform} content")

            # 逐个模块进行模拟分析
            cumulative_progress = 0

            for i, module in enumerate(self.analysis_modules):
                try:
                    logger.info(f"🔄 Analyzing module: {module['display_name']}")

                    # 模拟分析结果
                    mock_result = self._generate_mock_analysis(module, note_data, transcript_text)

                    # 模拟流式输出
                    chunks = self._split_into_chunks(mock_result)
                    accumulated_content = ""

                    for j, chunk in enumerate(chunks):
                        accumulated_content += chunk

                        # 计算进度
                        chunk_progress = cumulative_progress + (module["weight"] * (j + 1) / len(chunks))

                        # 返回流式内容块
                        yield AnalysisChunk(
                            chunk_type=f"{module['name']}_streaming",
                            chunk_data={
                                "module_name": module["display_name"],
                                "content_chunk": chunk,
                                "accumulated_content": accumulated_content,
                                "timestamp": asyncio.get_event_loop().time()
                            },
                            is_complete=False,
                            progress_percentage=int(chunk_progress)
                        )

                        # 模拟延迟
                        await asyncio.sleep(0.1)

                    # 解析最终结果
                    try:
                        module_result = self._parse_final_result(accumulated_content, module["name"])
                    except Exception as e:
                        logger.warning(f"Failed to parse final result for {module['name']}: {e}")
                        module_result = {
                            "analysis_text": accumulated_content,
                            "format": "text",
                            "module": module["name"]
                        }

                    # 计算最终进度
                    cumulative_progress += module["weight"]

                    # 返回完成的分析块
                    yield AnalysisChunk(
                        chunk_type=module["name"],
                        chunk_data={
                            "module_name": module["display_name"],
                            "result": module_result,
                            "full_content": accumulated_content,
                            "timestamp": asyncio.get_event_loop().time()
                        },
                        is_complete=True,
                        progress_percentage=cumulative_progress
                    )

                    # 添加小延迟，模拟真实的分析过程
                    await asyncio.sleep(0.5)

                except Exception as e:
                    logger.error(f"❌ Failed to analyze module {module['name']}: {e}")

                    # 返回错误块
                    yield AnalysisChunk(
                        chunk_type=module["name"],
                        chunk_data={
                            "module_name": module["display_name"],
                            "error": str(e),
                            "timestamp": asyncio.get_event_loop().time()
                        },
                        is_complete=False,
                        progress_percentage=cumulative_progress
                    )

            logger.info("✅ Streaming AI analysis completed")

        except Exception as e:
            logger.error(f"❌ Streaming AI analysis failed: {e}")

            # 返回最终错误块
            yield AnalysisChunk(
                chunk_type="error",
                chunk_data={
                    "error": str(e),
                    "timestamp": asyncio.get_event_loop().time()
                },
                is_complete=False,
                progress_percentage=0
            )

    def _prepare_analysis_data(
        self,
        note_data: Dict[str, Any],
        transcript_text: Optional[str],
        platform: str
    ) -> Dict[str, Any]:
        """准备分析数据"""

        # 提取基础信息
        title = note_data.get("title", "")
        description = note_data.get("description", "")
        author = note_data.get("author", {})
        tags = note_data.get("tags", []) or note_data.get("tag_list", [])

        # 提取互动数据
        interact_info = note_data.get("interact_info", {})
        if not interact_info:
            # 兼容不同的数据结构
            interact_info = {
                "liked_count": note_data.get("liked_count", 0),
                "collected_count": note_data.get("collected_count", 0),
                "comment_count": note_data.get("comment_count", 0),
                "share_count": note_data.get("share_count", 0)
            }

        return {
            "title": title,
            "description": description,
            "author": author.get("nickname", "") if isinstance(author, dict) else str(author),
            "note_type": note_data.get("type", note_data.get("note_type", "unknown")),
            "tags": ", ".join(tags) if tags else "无标签",
            "liked_count": interact_info.get("liked_count", 0),
            "collected_count": interact_info.get("collected_count", 0),
            "comment_count": interact_info.get("comment_count", 0),
            "share_count": interact_info.get("share_count", 0),
            "transcript_text": transcript_text or "无转录内容",
            "platform": platform,
            "has_video": bool(note_data.get("video_url")),
            "image_count": len(note_data.get("images", []) or note_data.get("image_list", []))
        }

    def _generate_module_prompt(
        self,
        module: Dict[str, Any],
        analysis_data: Dict[str, Any],
        custom_prompt: Optional[str] = None
    ) -> str:
        """生成模块特定的提示词"""

        base_info = f"""
## {analysis_data['platform']}内容信息：
- 标题：{analysis_data['title']}
- 描述：{analysis_data['description']}
- 作者：{analysis_data['author']}
- 类型：{analysis_data['note_type']}
- 标签：{analysis_data['tags']}
- 互动数据：点赞 {analysis_data['liked_count']}，收藏 {analysis_data['collected_count']}，评论 {analysis_data['comment_count']}
- 是否有视频：{analysis_data['has_video']}
- 图片数量：{analysis_data['image_count']}

## 转录内容：
{analysis_data['transcript_text']}
"""

        # 模块特定的提示词
        module_prompts = {
            "content_theme": f"""
{base_info}

请专门分析这个{analysis_data['platform']}内容的主题特征，以JSON格式返回：
{{
    "main_topics": ["主要话题1", "主要话题2"],
    "category": "内容分类",
    "target_audience": "目标受众描述",
    "keywords": ["关键词1", "关键词2"],
    "topic_relevance_score": 85
}}
""",
            "content_structure": f"""
{base_info}

请专门分析这个{analysis_data['platform']}内容的文案结构，以JSON格式返回：
{{
    "title_features": "标题特点分析",
    "content_organization": "内容组织方式",
    "language_style": "语言风格特征",
    "structure_score": 80,
    "readability": "可读性评价"
}}
""",
            "creative_highlights": f"""
{base_info}

请专门分析这个{analysis_data['platform']}内容的创意亮点，以JSON格式返回：
{{
    "unique_points": ["独特创意点1", "独特创意点2"],
    "visual_features": "视觉呈现特色",
    "interaction_design": "互动设计巧思",
    "creativity_score": 75,
    "innovation_level": "创新程度评价"
}}
""",
            "engagement_strategy": f"""
{base_info}

请专门分析这个{analysis_data['platform']}内容的传播策略，以JSON格式返回：
{{
    "posting_strategy": "发布策略分析",
    "hashtag_strategy": "标签使用策略",
    "user_engagement": "用户互动引导",
    "viral_potential": "传播潜力评估",
    "engagement_score": 70
}}
""",
            "emotional_analysis": f"""
{base_info}

请专门分析这个{analysis_data['platform']}内容的情感倾向，以JSON格式返回：
{{
    "overall_emotion": "整体情感色彩",
    "emotion_expression": "情感表达方式",
    "resonance_points": ["用户共鸣点1", "用户共鸣点2"],
    "emotional_intensity": "情感强度",
    "emotion_score": 80
}}
""",
            "optimization_suggestions": f"""
{base_info}

请专门为这个{analysis_data['platform']}内容提供优化建议，以JSON格式返回：
{{
    "title_optimization": "标题优化建议",
    "content_optimization": "内容优化建议",
    "engagement_optimization": "互动优化建议",
    "timing_suggestions": "发布时机建议",
    "overall_rating": 75
}}
"""
        }

        # 如果有自定义提示词，添加到末尾
        prompt = module_prompts.get(module["name"], base_info)
        if custom_prompt:
            prompt += f"\n\n## 额外要求：\n{custom_prompt}"

        return prompt

    async def _analyze_single_module_streaming(self, prompt: str, module_name: str) -> AsyncGenerator[str, None]:
        """流式分析单个模块"""
        try:
            logger.info(f"🔄 Starting streaming analysis for module: {module_name}")

            # 暂时使用模拟流式输出，直到DeepSeek配置完成
            # TODO: 替换为真正的DeepSeek流式调用

            # 模拟流式输出
            analysis_text = f"""
{{
    "module": "{module_name}",
    "analysis": "这是{module_name}的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}}
"""

            # 模拟逐字输出
            for i in range(0, len(analysis_text), 10):
                chunk = analysis_text[i:i+10]
                yield chunk
                await asyncio.sleep(0.1)  # 模拟网络延迟

        except Exception as e:
            logger.error(f"Error in streaming analysis for {module_name}: {e}")
            yield f"[分析错误: {str(e)}]"

    def _parse_final_result(self, content: str, module_name: str) -> Dict[str, Any]:
        """解析流式输出的最终结果"""
        try:
            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', content, re.DOTALL)
            if json_match:
                json_str = json_match.group()
                result = json.loads(json_str)
                return result
            else:
                # 如果没有找到JSON，返回文本结果
                return {
                    "analysis_text": content,
                    "format": "text",
                    "module": module_name
                }
        except json.JSONDecodeError as e:
            logger.warning(f"Failed to parse JSON response for {module_name}: {e}")
            return {
                "analysis_text": content,
                "format": "text",
                "module": module_name,
                "parse_error": str(e)
            }

    async def _analyze_single_module(self, prompt: str, module_name: str) -> Dict[str, Any]:
        """分析单个模块"""
        try:
            # 调用AI模型
            response = await ai_model_manager.generate_response(
                prompt=prompt,
                model_name="gpt-4",
                temperature=0.3,
                max_tokens=1500
            )

            if not response:
                raise Exception("AI model returned empty response")

            # 尝试解析JSON
            try:
                # 提取JSON部分
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    result = json.loads(json_str)
                    return result
                else:
                    # 如果没有找到JSON，返回文本结果
                    return {
                        "analysis_text": response,
                        "format": "text",
                        "module": module_name
                    }

            except json.JSONDecodeError as e:
                logger.warning(f"⚠️ Failed to parse JSON for module {module_name}: {e}")
                return {
                    "analysis_text": response,
                    "format": "text",
                    "parse_error": str(e),
                    "module": module_name
                }

        except Exception as e:
            logger.error(f"❌ Failed to analyze module {module_name}: {e}")
            return {
                "error": str(e),
                "module": module_name,
                "fallback_analysis": self._get_fallback_analysis(module_name)
            }

    def _get_fallback_analysis(self, module_name: str) -> Dict[str, Any]:
        """获取降级分析结果"""
        fallback_results = {
            "content_theme": {
                "main_topics": ["内容分析"],
                "category": "生活分享",
                "target_audience": "普通用户",
                "keywords": ["生活", "分享"],
                "topic_relevance_score": 60
            },
            "content_structure": {
                "title_features": "标题简洁明了",
                "content_organization": "图文结合",
                "language_style": "日常化表达",
                "structure_score": 60,
                "readability": "良好"
            },
            "creative_highlights": {
                "unique_points": ["真实记录"],
                "visual_features": "生活化场景",
                "interaction_design": "简单直接",
                "creativity_score": 50,
                "innovation_level": "一般"
            },
            "engagement_strategy": {
                "posting_strategy": "日常发布",
                "hashtag_strategy": "基础标签",
                "user_engagement": "自然互动",
                "viral_potential": "中等",
                "engagement_score": 50
            },
            "emotional_analysis": {
                "overall_emotion": "积极正面",
                "emotion_expression": "自然流露",
                "resonance_points": ["生活共鸣"],
                "emotional_intensity": "中等",
                "emotion_score": 60
            },
            "optimization_suggestions": {
                "title_optimization": "可以更加吸引眼球",
                "content_optimization": "增加互动元素",
                "engagement_optimization": "添加话题引导",
                "timing_suggestions": "选择活跃时段发布",
                "overall_rating": 60
            }
        }

        return fallback_results.get(module_name, {"error": "Unknown module"})

    def _generate_mock_analysis(self, module: Dict[str, Any], note_data: Dict[str, Any], transcript_text: Optional[str]) -> str:
        """生成模拟分析结果"""
        module_name = module["name"]

        mock_results = {
            "content_theme": '''
{
    "module": "content_theme",
    "analysis": "这是content_theme的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
''',
            "content_structure": '''
{
    "module": "content_structure",
    "analysis": "这是content_structure的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
''',
            "creative_highlights": '''
{
    "module": "creative_highlights",
    "analysis": "这是creative_highlights的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
''',
            "engagement_strategy": '''
{
    "module": "engagement_strategy",
    "analysis": "这是engagement_strategy的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
''',
            "emotional_analysis": '''
{
    "module": "emotional_analysis",
    "analysis": "这是emotional_analysis的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
''',
            "optimization_suggestions": '''
{
    "module": "optimization_suggestions",
    "analysis": "这是optimization_suggestions的详细分析结果",
    "key_points": [
        "关键点1：内容质量很高",
        "关键点2：用户互动良好",
        "关键点3：传播效果显著"
    ],
    "score": 85,
    "recommendations": [
        "建议1：优化标题吸引力",
        "建议2：增加互动元素"
    ]
}
'''
        }

        return mock_results.get(module_name, '{"error": "Unknown module"}')

    def _split_into_chunks(self, content: str) -> List[str]:
        """将内容分割成小块用于模拟流式输出"""
        # 简单按字符分割
        chunk_size = 20
        chunks = []
        for i in range(0, len(content), chunk_size):
            chunks.append(content[i:i + chunk_size])
        return chunks


# 全局流式AI分析器实例
streaming_ai_analyzer = StreamingAIAnalyzer()